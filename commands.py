# -*- coding: utf-8 -*-
"""
Comandos adicionales para el Bot de Sorteos
Desarrollado por NightyWolf
"""

import discord
from discord.ext import commands
import aiosqlite
import json
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List
from utils import (
    DatabaseManager, GiveawayManager, MessageFormatter, 
    SecurityManager, PerformanceMonitor, send_dm_safe
)
from config import COLORS, MESSAGES, LIMITS

class AdminCommands(commands.Cog):
    """Comandos de administración avanzados"""
    
    def __init__(self, bot):
        self.bot = bot
        self.db_manager = DatabaseManager()
        self.performance_monitor = PerformanceMonitor()
    
    @commands.command(name='backup')
    @commands.has_permissions(administrator=True)
    async def backup_database(self, ctx):
        """Crea un backup de la base de datos"""
        try:
            backup_path = await self.db_manager.backup_database()
            
            embed = discord.Embed(
                title='✅ Backup creado',
                description=f'Base de datos respaldada en: `{backup_path}`',
                color=COLORS['success']
            )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            embed = discord.Embed(
                title='❌ Error en backup',
                description=f'No se pudo crear el backup: {str(e)}',
                color=COLORS['error']
            )
            await ctx.send(embed=embed)
    
    @commands.command(name='stats')
    @commands.has_permissions(administrator=True)
    async def bot_stats(self, ctx):
        """Muestra estadísticas completas del bot"""
        try:
            db_stats = await self.db_manager.get_database_stats()
            perf_stats = self.performance_monitor.get_stats()
            
            embed = MessageFormatter.create_stats_embed(db_stats, self.bot.user)
            
            # Agregar estadísticas de rendimiento
            embed.add_field(
                name='⏱️ Tiempo activo',
                value=str(perf_stats['uptime']).split('.')[0],
                inline=True
            )
            
            embed.add_field(
                name='🔧 Comandos ejecutados',
                value=f"{perf_stats['commands_used']:,}",
                inline=True
            )
            
            embed.add_field(
                name='⚠️ Errores',
                value=f"{perf_stats['error_count']:,}",
                inline=True
            )
            
            if perf_stats['most_used_command']:
                cmd_name, cmd_count = perf_stats['most_used_command']
                embed.add_field(
                    name='🏆 Comando más usado',
                    value=f"`{cmd_name}` ({cmd_count} veces)",
                    inline=False
                )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            await ctx.send(f"❌ Error obteniendo estadísticas: {str(e)}")
    
    @commands.command(name='cleanup')
    @commands.has_permissions(administrator=True)
    async def cleanup_database(self, ctx):
        """Limpia datos expirados de la base de datos"""
        try:
            deleted_count = await self.db_manager.cleanup_expired_data()
            
            embed = discord.Embed(
                title='🧹 Limpieza completada',
                description=f'Se eliminaron {deleted_count} registros expirados.',
                color=COLORS['success']
            )
            
            await ctx.send(embed=embed)
            
        except Exception as e:
            await ctx.send(f"❌ Error en limpieza: {str(e)}")
    
    @commands.command(name='end_giveaway')
    @commands.has_permissions(manage_messages=True)
    async def force_end_giveaway(self, ctx, message_id: int):
        """Termina un sorteo manualmente"""
        try:
            async with aiosqlite.connect('bot_data.db') as db:
                cursor = await db.execute(
                    'SELECT * FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                giveaway = await cursor.fetchone()
                
                if not giveaway:
                    await ctx.send('❌ No se encontró un sorteo activo con ese ID.')
                    return
                
                # Terminar sorteo
                from bot import end_giveaway
                await end_giveaway(giveaway)
                
                # Eliminar de base de datos
                await db.execute(
                    'DELETE FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                await db.commit()
                
                embed = discord.Embed(
                    title='✅ Sorteo terminado',
                    description=f'El sorteo con ID {message_id} ha sido terminado manualmente.',
                    color=COLORS['success']
                )
                
                await ctx.send(embed=embed)
                
        except Exception as e:
            await ctx.send(f"❌ Error terminando sorteo: {str(e)}")
    
    @commands.command(name='list_giveaways')
    @commands.has_permissions(manage_messages=True)
    async def list_active_giveaways(self, ctx):
        """Lista todos los sorteos activos en el servidor"""
        try:
            async with aiosqlite.connect('bot_data.db') as db:
                cursor = await db.execute(
                    'SELECT message_id, title, end_time, winner_count FROM active_giveaways WHERE guild_id = ?',
                    (ctx.guild.id,)
                )
                giveaways = await cursor.fetchall()
                
                if not giveaways:
                    embed = discord.Embed(
                        title='📋 Sorteos activos',
                        description='No hay sorteos activos en este servidor.',
                        color=COLORS['info']
                    )
                    await ctx.send(embed=embed)
                    return
                
                embed = discord.Embed(
                    title='📋 Sorteos activos',
                    color=COLORS['info']
                )
                
                for message_id, title, end_time, winner_count in giveaways:
                    end_dt = datetime.fromisoformat(end_time)
                    time_left = end_dt - datetime.now()
                    
                    if time_left.total_seconds() > 0:
                        status = f"Termina <t:{int(end_dt.timestamp())}:R>"
                    else:
                        status = "⏰ Expirado"
                    
                    embed.add_field(
                        name=f"🎉 {title[:50]}{'...' if len(title) > 50 else ''}",
                        value=f"ID: `{message_id}`\nGanadores: {winner_count}\n{status}",
                        inline=True
                    )
                
                await ctx.send(embed=embed)
                
        except Exception as e:
            await ctx.send(f"❌ Error listando sorteos: {str(e)}")

class UserCommands(commands.Cog):
    """Comandos para usuarios regulares"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='my_entries')
    async def check_my_entries(self, ctx, message_id: int):
        """Verifica las entradas del usuario en un sorteo específico"""
        try:
            async with aiosqlite.connect('bot_data.db') as db:
                cursor = await db.execute(
                    'SELECT title, participants FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                giveaway = await cursor.fetchone()
                
                if not giveaway:
                    await ctx.send('❌ No se encontró un sorteo con ese ID.')
                    return
                
                title, participants_json = giveaway
                participants = json.loads(participants_json) if participants_json else []
                
                if ctx.author.id in participants:
                    # Calcular entradas basadas en roles
                    entries = await GiveawayManager.get_user_entries(ctx.author, {})
                    
                    embed = discord.Embed(
                        title='🎫 Tus entradas',
                        description=f'**Sorteo:** {title}\n'
                                   f'**Tus entradas:** {entries}\n'
                                   f'**Estado:** ✅ Participando',
                        color=COLORS['success']
                    )
                else:
                    embed = discord.Embed(
                        title='🎫 Tus entradas',
                        description=f'**Sorteo:** {title}\n'
                                   f'**Estado:** ❌ No estás participando',
                        color=COLORS['error']
                    )
                
                await ctx.send(embed=embed)
                
        except Exception as e:
            await ctx.send(f"❌ Error verificando entradas: {str(e)}")
    
    @commands.command(name='my_giveaways')
    async def my_created_giveaways(self, ctx):
        """Muestra los sorteos creados por el usuario"""
        try:
            async with aiosqlite.connect('bot_data.db') as db:
                cursor = await db.execute(
                    'SELECT message_id, title, end_time, winner_count FROM active_giveaways WHERE created_by = ? AND guild_id = ?',
                    (ctx.author.id, ctx.guild.id)
                )
                giveaways = await cursor.fetchall()
                
                if not giveaways:
                    embed = discord.Embed(
                        title='📋 Mis sorteos',
                        description='No has creado ningún sorteo activo.',
                        color=COLORS['info']
                    )
                    await ctx.send(embed=embed)
                    return
                
                embed = discord.Embed(
                    title='📋 Mis sorteos creados',
                    color=COLORS['info']
                )
                
                for message_id, title, end_time, winner_count in giveaways:
                    end_dt = datetime.fromisoformat(end_time)
                    
                    embed.add_field(
                        name=f"🎉 {title[:50]}{'...' if len(title) > 50 else ''}",
                        value=f"ID: `{message_id}`\nGanadores: {winner_count}\nTermina: <t:{int(end_dt.timestamp())}:R>",
                        inline=True
                    )
                
                await ctx.send(embed=embed)
                
        except Exception as e:
            await ctx.send(f"❌ Error obteniendo tus sorteos: {str(e)}")
    
    @commands.command(name='help_giveaway')
    async def giveaway_help(self, ctx):
        """Muestra ayuda detallada sobre sorteos"""
        embed = discord.Embed(
            title='🎉 Ayuda de Sorteos',
            description='Guía completa para usar el sistema de sorteos',
            color=COLORS['info']
        )
        
        embed.add_field(
            name='👥 Para Usuarios',
            value='• `!authorize` - Autorizar el bot\n'
                  '• `!confirm_auth` - Confirmar autorización\n'
                  '• `!my_entries <ID>` - Ver tus entradas\n'
                  '• `!help_giveaway` - Esta ayuda',
            inline=False
        )
        
        embed.add_field(
            name='🛠️ Para Administradores',
            value='• `!giveaway <tiempo> <ganadores> <premio>` - Crear sorteo\n'
                  '• `!setup <canales>` - Configurar bot\n'
                  '• `!end_giveaway <ID>` - Terminar sorteo\n'
                  '• `!list_giveaways` - Ver sorteos activos',
            inline=False
        )
        
        embed.add_field(
            name='⏰ Formatos de Tiempo',
            value='• `30s` - 30 segundos\n'
                  '• `5m` - 5 minutos\n'
                  '• `2h` - 2 horas\n'
                  '• `1d` - 1 día',
            inline=True
        )
        
        embed.add_field(
            name='📋 Ejemplos',
            value='• `!giveaway 1h 1 Nitro Discord`\n'
                  '• `!giveaway 30m 3 Roles especiales`\n'
                  '• `!setup #bienvenida #actividad #hitos`',
            inline=True
        )
        
        embed.add_field(
            name='⚠️ Requisitos',
            value='• Debes autorizar el bot para participar\n'
                  '• Los administradores pueden crear sorteos\n'
                  '• Algunos roles dan entradas extra',
            inline=False
        )
        
        await ctx.send(embed=embed)

class UtilityCommands(commands.Cog):
    """Comandos de utilidad"""
    
    def __init__(self, bot):
        self.bot = bot
    
    @commands.command(name='ping')
    async def ping(self, ctx):
        """Muestra la latencia del bot"""
        latency = round(self.bot.latency * 1000)
        
        if latency < 100:
            color = COLORS['success']
            status = "Excelente"
        elif latency < 200:
            color = COLORS['warning']
            status = "Buena"
        else:
            color = COLORS['error']
            status = "Alta"
        
        embed = discord.Embed(
            title='🏓 Pong!',
            description=f'**Latencia:** {latency}ms\n**Estado:** {status}',
            color=color
        )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='server_info')
    async def server_info(self, ctx):
        """Muestra información del servidor"""
        guild = ctx.guild
        
        embed = discord.Embed(
            title=f'📊 Información de {guild.name}',
            color=COLORS['info']
        )
        
        embed.set_thumbnail(url=guild.icon.url if guild.icon else None)
        
        embed.add_field(
            name='👥 Miembros',
            value=f'{guild.member_count:,}',
            inline=True
        )
        
        embed.add_field(
            name='📅 Creado',
            value=f'<t:{int(guild.created_at.timestamp())}:D>',
            inline=True
        )
        
        embed.add_field(
            name='👑 Dueño',
            value=guild.owner.mention if guild.owner else 'Desconocido',
            inline=True
        )
        
        embed.add_field(
            name='🎭 Roles',
            value=f'{len(guild.roles):,}',
            inline=True
        )
        
        embed.add_field(
            name='💬 Canales',
            value=f'{len(guild.channels):,}',
            inline=True
        )
        
        embed.add_field(
            name='😀 Emojis',
            value=f'{len(guild.emojis):,}',
            inline=True
        )
        
        # Verificar configuración del bot
        async with aiosqlite.connect('bot_data.db') as db:
            cursor = await db.execute(
                'SELECT welcome_channel, activity_channel, milestone_channel FROM guild_config WHERE guild_id = ?',
                (guild.id,)
            )
            config = await cursor.fetchone()
            
            if config:
                configured_channels = sum(1 for channel in config if channel)
                embed.add_field(
                    name='🤖 Bot configurado',
                    value=f'✅ {configured_channels}/3 canales',
                    inline=True
                )
            else:
                embed.add_field(
                    name='🤖 Bot configurado',
                    value='❌ No configurado',
                    inline=True
                )
        
        await ctx.send(embed=embed)
    
    @commands.command(name='user_info')
    async def user_info(self, ctx, user: Optional[discord.Member] = None):
        """Muestra información de un usuario"""
        if user is None:
            user = ctx.author
        
        embed = discord.Embed(
            title=f'👤 Información de {user.display_name}',
            color=user.color if user.color != discord.Color.default() else COLORS['info']
        )
        
        embed.set_thumbnail(url=user.avatar.url if user.avatar else user.default_avatar.url)
        
        embed.add_field(
            name='🏷️ Nombre de usuario',
            value=f'{user.name}#{user.discriminator}',
            inline=True
        )
        
        embed.add_field(
            name='🆔 ID',
            value=f'`{user.id}`',
            inline=True
        )
        
        embed.add_field(
            name='📅 Cuenta creada',
            value=f'<t:{int(user.created_at.timestamp())}:D>',
            inline=True
        )
        
        embed.add_field(
            name='📥 Se unió al servidor',
            value=f'<t:{int(user.joined_at.timestamp())}:D>' if user.joined_at else 'Desconocido',
            inline=True
        )
        
        embed.add_field(
            name='🎭 Roles',
            value=f'{len(user.roles) - 1}',  # -1 para excluir @everyone
            inline=True
        )
        
        # Verificar si está autorizado
        async with aiosqlite.connect('bot_data.db') as db:
            cursor = await db.execute(
                'SELECT user_id FROM authorized_users WHERE user_id = ?',
                (user.id,)
            )
            is_authorized = await cursor.fetchone() is not None
            
            embed.add_field(
                name='🔐 Autorizado para sorteos',
                value='✅ Sí' if is_authorized else '❌ No',
                inline=True
            )
        
        # Mostrar roles principales (máximo 5)
        if len(user.roles) > 1:
            top_roles = sorted(user.roles[1:], key=lambda r: r.position, reverse=True)[:5]
            roles_text = ' '.join([role.mention for role in top_roles])
            if len(user.roles) > 6:
                roles_text += f' y {len(user.roles) - 6} más...'
            
            embed.add_field(
                name='🏆 Roles principales',
                value=roles_text,
                inline=False
            )
        
        await ctx.send(embed=embed)

# Función para agregar todos los cogs
def setup_commands(bot):
    """Configura todos los comandos adicionales"""
    bot.add_cog(AdminCommands(bot))
    bot.add_cog(UserCommands(bot))
    bot.add_cog(UtilityCommands(bot))
    
    print("✅ Comandos adicionales cargados")

# Manejador de errores global
@commands.Cog.listener()
async def on_command_error(ctx, error):
    """Maneja errores de comandos"""
    if isinstance(error, commands.CommandNotFound):
        return  # Ignorar comandos no encontrados
    
    elif isinstance(error, commands.MissingPermissions):
        embed = discord.Embed(
            title='❌ Permisos insuficientes',
            description='No tienes permisos para usar este comando.',
            color=COLORS['error']
        )
        await ctx.send(embed=embed)
    
    elif isinstance(error, commands.MissingRequiredArgument):
        embed = discord.Embed(
            title='❌ Argumento faltante',
            description=f'Falta el argumento: `{error.param.name}`\n'
                       f'Usa `!help {ctx.command.name}` para ver la sintaxis.',
            color=COLORS['error']
        )
        await ctx.send(embed=embed)
    
    elif isinstance(error, commands.BadArgument):
        embed = discord.Embed(
            title='❌ Argumento inválido',
            description='Uno de los argumentos proporcionados no es válido.',
            color=COLORS['error']
        )
        await ctx.send(embed=embed)
    
    elif isinstance(error, commands.CommandOnCooldown):
        embed = discord.Embed(
            title='⏰ Comando en cooldown',
            description=f'Puedes usar este comando de nuevo en {error.retry_after:.1f} segundos.',
            color=COLORS['warning']
        )
        await ctx.send(embed=embed)
    
    else:
        # Error inesperado
        embed = discord.Embed(
            title='❌ Error inesperado',
            description='Ocurrió un error inesperado. El desarrollador ha sido notificado.',
            color=COLORS['error']
        )
        await ctx.send(embed=embed)
        
        # Log del error
        print(f"Error en comando {ctx.command}: {error}")
        print(f"Usuario: {ctx.author} ({ctx.author.id})")
        print(f"Servidor: {ctx.guild} ({ctx.guild.id})")
        print(f"Canal: {ctx.channel} ({ctx.channel.id})")