# Configuración del Bot de Sorteos
# Personaliza estos valores según tus necesidades

# Configuración de sorteos
GIVEAWAY_CONFIG = {
    'default_duration': 3600,  # 1 hora en segundos
    'min_duration': 10,  # Duración mínima en segundos
    'max_duration': 604800,  # 1 semana en segundos
    'max_winners': 20,  # Máximo número de ganadores
    'reaction_emoji': '🎉',  # Emoji para participar en sorteos
    'quick_giveaway_emoji': '⚡',  # Emoji para sorteos rápidos
    'milestone_emoji': '🎊'  # Emoji para sorteos por hitos
}

# Configuración de mensajes
MESSAGES = {
    'welcome_message': '¡Bienvenido {mention}! 🎉',
    'welcome_delete_delay': 1,  # Segundos antes de eliminar mensaje de bienvenida
    
    'auth_required': (
        '❌ Para participar en sorteos, debes autorizar al bot para unirte a otros servidores.\n'
        'Usa el comando `!authorize` en el servidor para obtener el enlace de autorización.'
    ),
    
    'auth_success': '✅ Autorización confirmada. ¡Ahora puedes participar en todos los sorteos!',
    
    'giveaway_ended_no_participants': '😢 No hubo participantes válidos en el sorteo.',
    'giveaway_ended_no_winners': '😢 No se pudieron contactar a los ganadores.',
    
    'quick_giveaway_timeout': '⏰ Sorteo rápido expirado. Nadie reaccionó a tiempo.',
    'quick_giveaway_auth_required': '{mention}, necesitas estar autorizado para ganar. Usa `!authorize`.'
}

# Configuración de actividad
ACTIVITY_CONFIG = {
    'default_message_count': 50,  # Mensajes necesarios para sorteo rápido
    'quick_giveaway_timeout': 30,  # Segundos para reaccionar en sorteo rápido
    'quick_giveaway_prizes': [
        '💎 Gemas especiales',
        '🎁 Premio sorpresa',
        '⭐ Rol temporal especial',
        '🏆 Reconocimiento público',
        '🎨 Avatar personalizado',
        '💰 Monedas del servidor'
    ]
}

# Configuración de hitos
MILESTONE_CONFIG = {
    'default_milestone': 500,  # Miembros para activar sorteo
    'milestone_duration': 86400,  # 24 horas en segundos
    'milestone_prizes': [
        '🎊 Premio especial por hito',
        '💰 Recompensa exclusiva de crecimiento',
        '🌟 Regalo de celebración',
        '👑 Rol especial de hito',
        '🎯 Acceso a canal VIP',
        '🚀 Boost del servidor'
    ]
}

# Configuración de roles y multiplicadores
ROLE_CONFIG = {
    'max_multiplier': 10,  # Máximo multiplicador de entradas
    'min_multiplier': 1,   # Mínimo multiplicador de entradas
    'default_multiplier': 1
}

# Configuración de límites
LIMITS = {
    'max_invite_users': 50,  # Máximo usuarios a invitar por comando
    'max_giveaway_description': 1000,  # Caracteres máximos en descripción
    'max_concurrent_giveaways': 10,  # Sorteos simultáneos por servidor
    'rate_limit_delay': 1  # Segundos entre invitaciones
}

# Configuración de colores (hex)
COLORS = {
    'success': 0x00ff00,
    'error': 0xff0000,
    'warning': 0xffff00,
    'info': 0x3498db,
    'giveaway': 0xff6b6b,
    'quick_giveaway': 0xffff00,
    'milestone': 0xff1493,
    'auth': 0x3498db
}

# Configuración de base de datos
DATABASE_CONFIG = {
    'db_name': 'bot_data.db',
    'backup_interval': 3600,  # Backup cada hora
    'cleanup_interval': 86400  # Limpieza diaria
}

# Configuración de logging
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'bot.log',
    'max_size': 10485760,  # 10MB
    'backup_count': 5
}

# URLs y enlaces
URLS = {
    'support_server': 'https://discord.gg/tu-servidor-soporte',
    'documentation': 'https://tu-documentacion.com',
    'privacy_policy': 'https://tu-politica-privacidad.com'
}

# Configuración de desarrollo
DEV_CONFIG = {
    'debug_mode': False,
    'test_guild_id': None,  # ID del servidor de pruebas
    'dev_user_ids': [],  # IDs de desarrolladores
    'beta_features': False
}

# Configuración de performance
PERFORMANCE_CONFIG = {
    'cache_size': 1000,
    'message_cache_limit': 5000,
    'member_cache_flags': {
        'online': True,
        'offline': False
    }
}

# Textos personalizables
CUSTOM_TEXTS = {
    'bot_status': 'Gestionando sorteos 🎉',
    'footer_text': 'Bot de Sorteos v1.0',
    'embed_author': 'Sistema de Sorteos',
    'help_description': 'Bot completo de sorteos con sistema de autorizaciones'
}

# Configuración de seguridad
SECURITY_CONFIG = {
    'require_auth_for_giveaways': True,
    'max_auth_attempts': 3,
    'auth_cooldown': 300,  # 5 minutos
    'admin_only_commands': [
        'setup', 'giveaway', 'auth_stats', 'invite_users', 
        'set_role_multiplier', 'end_giveaway'
    ]
}

# Configuración de notificaciones
NOTIFICATION_CONFIG = {
    'dm_on_win': True,
    'dm_on_auth_required': True,
    'ping_on_milestone': True,
    'announce_giveaway_start': True,
    'announce_giveaway_end': True
}