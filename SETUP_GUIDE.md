# 🚀 Guía de Configuración Rápida - Bot de Sorteos

## ⚠️ E<PERSON><PERSON>: "PrivilegedIntentsRequired"

Si ves este error al ejecutar el bot:
```
discord.errors.PrivilegedIntentsRequired: Shard ID None is requesting privileged intents...
```

### 🔧 Solución:

1. **Ve al Discord Developer Portal**:
   - Abre: https://discord.com/developers/applications
   - Selecciona tu aplicación

2. **Habilita los Intents Privilegiados**:
   - Ve a la sección "Bot" en el menú lateral
   - Desplázate hacia abajo hasta "Privileged Gateway Intents"
   - **HABILITA TODOS** los siguientes:
     - ✅ **Presence Intent**
     - ✅ **Server Members Intent**
     - ✅ **Message Content Intent**

3. **Guarda los cambios**:
   - Haz clic en "Save Changes"
   - Espera unos segundos

4. **Reinicia el bot**:
   ```bash
   python bot.py
   ```

## 🎯 Configuración Inicial Completa

### Paso 1: Crear el Bot
1. Ve a https://discord.com/developers/applications
2. Clic en "New Application"
3. Nombra tu aplicación (ej: "Bot de Sorteos")
4. Ve a la sección "Bot"
5. Clic en "Add Bot"

### Paso 2: Configurar Token
1. En la sección "Bot", copia el token
2. Pégalo en el archivo `.env`:
   ```
   DISCORD_TOKEN=tu_token_aqui
   BOT_APPLICATION_ID=tu_application_id_aqui
   ```

### Paso 3: Habilitar Intents (CRÍTICO)
- En "Privileged Gateway Intents":
  - ✅ Presence Intent
  - ✅ Server Members Intent  
  - ✅ Message Content Intent

### Paso 4: Invitar el Bot
1. Ve a la sección "OAuth2" > "URL Generator"
2. Selecciona scopes:
   - ✅ `bot`
   - ✅ `applications.commands`
3. Selecciona permisos:
   - ✅ Administrator (recomendado)
   - O permisos específicos:
     - Manage Channels
     - Manage Messages
     - Send Messages
     - Add Reactions
     - Read Message History
     - Use External Emojis
     - Manage Roles
4. Copia la URL generada e invita el bot a tu servidor

### Paso 5: Ejecutar el Bot
```bash
# Opción 1: Script de inicio
start_bot.bat

# Opción 2: Comando directo
python bot.py
```

### Paso 6: Configurar en Discord
Una vez que el bot esté online:

```
!setup #canal-bienvenida #canal-actividad #canal-hitos
```

## 🎮 Comandos de Prueba

```bash
# Crear un sorteo de prueba
!giveaway 1m 1 Premio de Prueba

# Ver estadísticas
!auth_stats

# Obtener enlace de autorización
!authorize
```

## 🐛 Solución de Problemas

### Bot no responde:
- ✅ Verifica que los intents estén habilitados
- ✅ Confirma que el token sea correcto
- ✅ Asegúrate de que el bot tenga permisos en el canal

### Error de permisos:
- ✅ El bot necesita permisos de administrador
- ✅ O permisos específicos listados arriba

### Error de base de datos:
- ✅ El archivo `bot_data.db` se crea automáticamente
- ✅ Asegúrate de tener permisos de escritura en la carpeta

## 📞 Soporte

Si tienes problemas:
1. Revisa esta guía completa
2. Verifica el archivo `README.md`
3. Contacta a **NightyWolf** para soporte técnico

---

**Desarrollado por NightyWolf** 🐺
**Compatible con Python 3.8+ y 1GB RAM** 💻