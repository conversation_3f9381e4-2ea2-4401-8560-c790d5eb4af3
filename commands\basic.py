"""
Basic bot commands with memory optimization.
Implements essential bot functionality with minimal resource usage.
"""
import discord
from discord.ext import commands
from typing import Optional

from core.logging import logger
from embeds.builders import EmbedTemplates, InfoEmbed
from interactions.views.base import ConfirmationView
from utils.database import db_manager

def setup_basic_commands(bot: commands.Bot):
    """Setup basic bot commands"""
    
    @bot.command(name='ping')
    async def ping_command(ctx):
        """Check bot latency"""
        latency = round(bot.latency * 1000)
        
        embed = InfoEmbed(
            "🏓 Pong!",
            f"**Latency:** {latency}ms\n"
            f"**Status:** Online\n"
            f"**Memory Optimized:** Yes"
        ).build()
        
        await ctx.send(embed=embed)
        logger.debug(f"Ping command used by {ctx.author} - {latency}ms")
    
    @bot.command(name='info', aliases=['about'])
    async def info_command(ctx):
        """Show bot information"""
        uptime = bot.get_uptime()
        uptime_str = f"{uptime.days}d {uptime.seconds//3600}h {(uptime.seconds//60)%60}m"
        
        embed = EmbedTemplates.bot_info(
            bot.user,
            uptime_str,
            len(bot.guilds)
        )
        
        await ctx.send(embed=embed)
        logger.debug(f"Info command used by {ctx.author}")
    
    @bot.command(name='help')
    async def help_command(ctx, command_name: Optional[str] = None):
        """Show help information"""
        if command_name:
            # Show help for specific command
            command = bot.get_command(command_name)
            if command:
                embed = InfoEmbed(
                    f"Help: {command.name}",
                    f"**Description:** {command.help or 'No description available'}\n"
                    f"**Usage:** `{ctx.prefix}{command.name} {command.signature}`"
                ).build()
            else:
                embed = EmbedTemplates.create_error_embed(
                    "Command Not Found",
                    f"Command `{command_name}` does not exist."
                )
        else:
            # Show general help
            embed = InfoEmbed(
                "🤖 Bot Commands",
                "**Basic Commands:**\n"
                f"`{ctx.prefix}ping` - Check bot latency\n"
                f"`{ctx.prefix}info` - Show bot information\n"
                f"`{ctx.prefix}help [command]` - Show help\n\n"
                "**Giveaway Commands:**\n"
                f"`{ctx.prefix}authorize` - Authorize for giveaways\n"
                f"`{ctx.prefix}giveaway` - Create giveaway (Admin)\n\n"
                "**Admin Commands:**\n"
                f"`{ctx.prefix}setup` - Configure server (Admin)\n"
                f"`{ctx.prefix}auth_stats` - View auth stats (Admin)"
            ).set_footer("Use !help <command> for detailed help").build()
        
        await ctx.send(embed=embed)
        logger.debug(f"Help command used by {ctx.author}")
    
    @bot.command(name='authorize')
    async def authorize_command(ctx):
        """Provide authorization link for giveaways"""
        auth_url = f'https://discord.com/api/oauth2/authorize?client_id={bot.user.id}&permissions=1&scope=bot%20guilds.join'
        
        embed = InfoEmbed(
            "🔐 Authorization Required",
            f"To participate in giveaways, you need to authorize the bot.\n\n"
            f"[**Click here to authorize**]({auth_url})\n\n"
            f"After authorization, use `{ctx.prefix}confirm_auth` to confirm."
        ).build()
        
        try:
            await ctx.author.send(embed=embed)
            await ctx.send("📩 I've sent you a DM with the authorization link!")
        except discord.Forbidden:
            await ctx.send(embed=embed)
        
        logger.debug(f"Authorization link sent to {ctx.author}")
    
    @bot.command(name='confirm_auth')
    async def confirm_auth_command(ctx):
        """Confirm user authorization"""
        success = await db_manager.add_authorized_user(ctx.author.id, str(ctx.author))
        
        if success:
            embed = EmbedTemplates.authorization_success()
            await ctx.send(embed=embed)
            logger.info(f"User authorized: {ctx.author} ({ctx.author.id})")
        else:
            embed = EmbedTemplates.create_error_embed(
                "Authorization Failed",
                "There was an error confirming your authorization. Please try again."
            )
            await ctx.send(embed=embed)
            logger.error(f"Failed to authorize user: {ctx.author}")
    
    @bot.event
    async def on_command_error(ctx, error):
        """Handle command errors"""
        if isinstance(error, commands.CommandNotFound):
            return  # Ignore unknown commands
        
        elif isinstance(error, commands.MissingPermissions):
            embed = EmbedTemplates.create_error_embed(
                "Missing Permissions",
                "You don't have permission to use this command."
            )
            await ctx.send(embed=embed)
        
        elif isinstance(error, commands.MissingRequiredArgument):
            embed = EmbedTemplates.create_error_embed(
                "Missing Argument",
                f"Missing required argument: `{error.param.name}`\n"
                f"Use `{ctx.prefix}help {ctx.command.name}` for usage information."
            )
            await ctx.send(embed=embed)
        
        elif isinstance(error, commands.BadArgument):
            embed = EmbedTemplates.create_error_embed(
                "Invalid Argument",
                f"Invalid argument provided.\n"
                f"Use `{ctx.prefix}help {ctx.command.name}` for usage information."
            )
            await ctx.send(embed=embed)
        
        elif isinstance(error, commands.CommandOnCooldown):
            embed = EmbedTemplates.create_warning_embed(
                "Command on Cooldown",
                f"Please wait {error.retry_after:.1f} seconds before using this command again."
            )
            await ctx.send(embed=embed)
        
        else:
            # Log unexpected errors
            logger.error(f"Unexpected command error: {error}")
            embed = EmbedTemplates.create_error_embed(
                "Unexpected Error",
                "An unexpected error occurred. Please try again later."
            )
            await ctx.send(embed=embed)
    
    logger.info("Basic commands loaded")
