"""
Memory-efficient logging system for Discord bot.
Focuses on minimal memory usage and structured output.
"""
import logging
import sys
from typing import Optional
from datetime import datetime

class MemoryEfficientFormatter(logging.Formatter):
    """Custom formatter that minimizes memory usage"""
    
    def __init__(self):
        # Simple format to reduce memory overhead
        super().__init__(
            fmt='%(asctime)s | %(levelname)s | %(name)s | %(message)s',
            datefmt='%H:%M:%S'
        )
    
    def format(self, record):
        # Override to avoid storing unnecessary data
        record.asctime = datetime.now().strftime('%H:%M:%S')
        return super().format(record)

class MemoryHandler(logging.StreamHandler):
    """Memory-efficient stream handler"""
    
    def __init__(self, stream=None):
        super().__init__(stream or sys.stdout)
        self.setFormatter(MemoryEfficientFormatter())
    
    def emit(self, record):
        try:
            super().emit(record)
            # Force flush to avoid buffering
            self.flush()
        except Exception:
            self.handleError(record)

def setup_logging(level: str = "INFO", name: Optional[str] = None) -> logging.Logger:
    """
    Set up memory-efficient logging.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR)
        name: Logger name (defaults to 'bot')
    
    Returns:
        Configured logger instance
    """
    logger_name = name or 'bot'
    logger = logging.getLogger(logger_name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Set level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Add memory-efficient handler
    handler = MemoryHandler()
    handler.setLevel(numeric_level)
    logger.addHandler(handler)
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    return logger

# Create default logger
logger = setup_logging()

# Convenience functions for common log levels
def debug(message: str, **kwargs):
    """Log debug message"""
    logger.debug(message, **kwargs)

def info(message: str, **kwargs):
    """Log info message"""
    logger.info(message, **kwargs)

def warning(message: str, **kwargs):
    """Log warning message"""
    logger.warning(message, **kwargs)

def error(message: str, **kwargs):
    """Log error message"""
    logger.error(message, **kwargs)

def critical(message: str, **kwargs):
    """Log critical message"""
    logger.critical(message, **kwargs)
