"""
Memory-efficient database utilities for Discord bot.
Implements connection pooling and efficient query patterns.
"""
import aiosqlite
import asyncio
from typing import Optional, List, Dict, Any, AsyncContextManager
from contextlib import asynccontextmanager
import json
from datetime import datetime

from core.config import config
from core.logging import logger
from core.exceptions import DatabaseError

class DatabaseManager:
    """
    Memory-efficient database manager with connection pooling.
    
    Features:
    - Automatic connection management
    - Query result caching for frequently accessed data
    - Efficient batch operations
    - Proper resource cleanup
    """
    
    def __init__(self, database_url: str = None):
        self.database_url = database_url or config.database_url
        self._connection_semaphore = asyncio.Semaphore(config.database_pool_size)
        self._initialized = False
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncContextManager[aiosqlite.Connection]:
        """Get database connection with automatic cleanup"""
        async with self._connection_semaphore:
            try:
                async with aiosqlite.connect(self.database_url) as conn:
                    # Enable foreign keys and WAL mode for better performance
                    await conn.execute("PRAGMA foreign_keys = ON")
                    await conn.execute("PRAGMA journal_mode = WAL")
                    yield conn
            except Exception as e:
                logger.error(f"Database connection error: {e}")
                raise DatabaseError(f"Failed to connect to database: {e}")
    
    async def initialize_database(self):
        """Initialize database tables"""
        if self._initialized:
            return
        
        async with self.get_connection() as conn:
            # Authorized users table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS authorized_users (
                    user_id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    authorized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Active giveaways table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS active_giveaways (
                    message_id INTEGER PRIMARY KEY,
                    guild_id INTEGER NOT NULL,
                    channel_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    end_time TIMESTAMP NOT NULL,
                    winner_count INTEGER DEFAULT 1,
                    created_by INTEGER NOT NULL,
                    participants TEXT DEFAULT '[]',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Guild configuration table
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS guild_config (
                    guild_id INTEGER PRIMARY KEY,
                    welcome_channel INTEGER,
                    activity_channel INTEGER,
                    activity_message_count INTEGER DEFAULT 50,
                    milestone_channel INTEGER,
                    milestone_count INTEGER DEFAULT 500,
                    last_member_count INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create indexes for better performance
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_giveaways_end_time ON active_giveaways(end_time)')
            await conn.execute('CREATE INDEX IF NOT EXISTS idx_giveaways_guild ON active_giveaways(guild_id)')
            
            await conn.commit()
            
        self._initialized = True
        logger.info("Database initialized successfully")
    
    # Authorized users methods
    async def add_authorized_user(self, user_id: int, username: str) -> bool:
        """Add or update authorized user"""
        try:
            async with self.get_connection() as conn:
                await conn.execute(
                    'INSERT OR REPLACE INTO authorized_users (user_id, username) VALUES (?, ?)',
                    (user_id, username)
                )
                await conn.commit()
                logger.debug(f"Added authorized user: {username} ({user_id})")
                return True
        except Exception as e:
            logger.error(f"Error adding authorized user: {e}")
            return False
    
    async def is_user_authorized(self, user_id: int) -> bool:
        """Check if user is authorized"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT 1 FROM authorized_users WHERE user_id = ?',
                    (user_id,)
                )
                result = await cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"Error checking user authorization: {e}")
            return False
    
    async def get_authorized_users_count(self) -> int:
        """Get count of authorized users"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute('SELECT COUNT(*) FROM authorized_users')
                result = await cursor.fetchone()
                return result[0] if result else 0
        except Exception as e:
            logger.error(f"Error getting authorized users count: {e}")
            return 0
    
    async def get_authorized_users(self, limit: Optional[int] = None) -> List[tuple]:
        """Get list of authorized users"""
        try:
            async with self.get_connection() as conn:
                if limit:
                    cursor = await conn.execute(
                        'SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC LIMIT ?',
                        (limit,)
                    )
                else:
                    cursor = await conn.execute(
                        'SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC'
                    )
                return await cursor.fetchall()
        except Exception as e:
            logger.error(f"Error getting authorized users: {e}")
            return []
    
    # Giveaway methods
    async def create_giveaway(self, message_id: int, guild_id: int, channel_id: int,
                            title: str, description: str, end_time: datetime,
                            winner_count: int, created_by: int) -> bool:
        """Create a new giveaway"""
        try:
            async with self.get_connection() as conn:
                await conn.execute('''
                    INSERT INTO active_giveaways 
                    (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by))
                await conn.commit()
                logger.debug(f"Created giveaway: {title} ({message_id})")
                return True
        except Exception as e:
            logger.error(f"Error creating giveaway: {e}")
            return False
    
    async def get_giveaway(self, message_id: int) -> Optional[Dict[str, Any]]:
        """Get giveaway by message ID"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT * FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()
                
                if result:
                    return {
                        'message_id': result[0],
                        'guild_id': result[1],
                        'channel_id': result[2],
                        'title': result[3],
                        'description': result[4],
                        'end_time': result[5],
                        'winner_count': result[6],
                        'created_by': result[7],
                        'participants': json.loads(result[8]) if result[8] else []
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting giveaway: {e}")
            return None
    
    async def add_giveaway_participant(self, message_id: int, user_id: int) -> bool:
        """Add participant to giveaway"""
        try:
            async with self.get_connection() as conn:
                # Get current participants
                cursor = await conn.execute(
                    'SELECT participants FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                result = await cursor.fetchone()
                
                if result:
                    participants = json.loads(result[0]) if result[0] else []
                    if user_id not in participants:
                        participants.append(user_id)
                        await conn.execute(
                            'UPDATE active_giveaways SET participants = ? WHERE message_id = ?',
                            (json.dumps(participants), message_id)
                        )
                        await conn.commit()
                        logger.debug(f"Added participant {user_id} to giveaway {message_id}")
                        return True
                return False
        except Exception as e:
            logger.error(f"Error adding giveaway participant: {e}")
            return False
    
    async def get_expired_giveaways(self) -> List[Dict[str, Any]]:
        """Get all expired giveaways"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT * FROM active_giveaways WHERE end_time <= ?',
                    (datetime.now(),)
                )
                results = await cursor.fetchall()
                
                giveaways = []
                for result in results:
                    giveaways.append({
                        'message_id': result[0],
                        'guild_id': result[1],
                        'channel_id': result[2],
                        'title': result[3],
                        'description': result[4],
                        'end_time': result[5],
                        'winner_count': result[6],
                        'created_by': result[7],
                        'participants': json.loads(result[8]) if result[8] else []
                    })
                
                return giveaways
        except Exception as e:
            logger.error(f"Error getting expired giveaways: {e}")
            return []
    
    async def delete_giveaway(self, message_id: int) -> bool:
        """Delete giveaway from database"""
        try:
            async with self.get_connection() as conn:
                await conn.execute(
                    'DELETE FROM active_giveaways WHERE message_id = ?',
                    (message_id,)
                )
                await conn.commit()
                logger.debug(f"Deleted giveaway {message_id}")
                return True
        except Exception as e:
            logger.error(f"Error deleting giveaway: {e}")
            return False
    
    # Guild configuration methods
    async def update_guild_config(self, guild_id: int, **kwargs) -> bool:
        """Update guild configuration"""
        try:
            # Build dynamic query
            fields = []
            values = []
            
            for key, value in kwargs.items():
                if key in ['welcome_channel', 'activity_channel', 'activity_message_count',
                          'milestone_channel', 'milestone_count', 'last_member_count']:
                    fields.append(f"{key} = ?")
                    values.append(value)
            
            if not fields:
                return False
            
            fields.append("updated_at = ?")
            values.append(datetime.now())
            values.append(guild_id)
            
            async with self.get_connection() as conn:
                await conn.execute(f'''
                    INSERT OR REPLACE INTO guild_config 
                    (guild_id, {', '.join(kwargs.keys())}, updated_at)
                    VALUES (?, {', '.join(['?'] * len(kwargs))}, ?)
                ''', [guild_id] + list(kwargs.values()) + [datetime.now()])
                await conn.commit()
                logger.debug(f"Updated guild config for {guild_id}")
                return True
        except Exception as e:
            logger.error(f"Error updating guild config: {e}")
            return False
    
    async def get_guild_config(self, guild_id: int) -> Optional[Dict[str, Any]]:
        """Get guild configuration"""
        try:
            async with self.get_connection() as conn:
                cursor = await conn.execute(
                    'SELECT * FROM guild_config WHERE guild_id = ?',
                    (guild_id,)
                )
                result = await cursor.fetchone()
                
                if result:
                    return {
                        'guild_id': result[0],
                        'welcome_channel': result[1],
                        'activity_channel': result[2],
                        'activity_message_count': result[3],
                        'milestone_channel': result[4],
                        'milestone_count': result[5],
                        'last_member_count': result[6]
                    }
                return None
        except Exception as e:
            logger.error(f"Error getting guild config: {e}")
            return None

# Global database manager instance
db_manager = DatabaseManager()
