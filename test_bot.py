#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar el funcionamiento del Bot de Sorteos
Desarrollado por NightyWolf
"""

import asyncio
import aiosqlite
import os
from pathlib import Path

async def test_database():
    """Prueba la conexión y estructura de la base de datos"""
    print("🔍 Probando base de datos...")
    
    try:
        async with aiosqlite.connect('bot_data.db') as db:
            # Verificar tablas
            cursor = await db.execute(
                "SELECT name FROM sqlite_master WHERE type='table';"
            )
            tables = await cursor.fetchall()
            
            expected_tables = ['authorized_users', 'active_giveaways', 'guild_config']
            existing_tables = [table[0] for table in tables]
            
            print(f"   Tablas encontradas: {existing_tables}")
            
            for table in expected_tables:
                if table in existing_tables:
                    print(f"   ✅ Tabla '{table}' existe")
                else:
                    print(f"   ❌ Tabla '{table}' faltante")
                    
            return True
            
    except Exception as e:
        print(f"   ❌ Error de base de datos: {e}")
        return False

def test_files():
    """Verifica que todos los archivos necesarios existan"""
    print("📁 Verificando archivos...")
    
    required_files = [
        'bot.py',
        'config.py', 
        'utils.py',
        'commands.py',
        'requirements.txt',
        '.env',
        'README.md',
        'SETUP_GUIDE.md'
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} faltante")
            all_exist = False
            
    return all_exist

def test_env_config():
    """Verifica la configuración del entorno"""
    print("⚙️ Verificando configuración...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("   ❌ Archivo .env no encontrado")
        return False
        
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
        
    if 'DISCORD_TOKEN=' in content:
        if 'tu_token_aqui' not in content:
            print("   ✅ Token configurado")
        else:
            print("   ⚠️ Token no configurado (usa token de prueba)")
    else:
        print("   ❌ Variable DISCORD_TOKEN no encontrada")
        return False
        
    return True

def test_imports():
    """Prueba las importaciones de dependencias"""
    print("📦 Probando dependencias...")
    
    dependencies = {
        'discord': 'discord.py',
        'aiosqlite': 'aiosqlite', 
        'dotenv': 'python-dotenv',
        'requests': 'requests'
    }
    
    all_imported = True
    for module, package in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {package}")
        except ImportError as e:
            print(f"   ❌ {package}: {e}")
            all_imported = False
            
    return all_imported

async def main():
    """Función principal de prueba"""
    print("\n" + "="*50)
    print("🎉 PRUEBA DEL BOT DE SORTEOS 🎉")
    print("Desarrollado por NightyWolf")
    print("="*50 + "\n")
    
    tests = [
        ("Archivos del proyecto", test_files),
        ("Configuración del entorno", test_env_config), 
        ("Dependencias de Python", test_imports),
        ("Base de datos", test_database)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
            
        results.append(result)
        
        if result:
            print(f"   ✅ {test_name}: PASÓ")
        else:
            print(f"   ❌ {test_name}: FALLÓ")
    
    print("\n" + "="*50)
    print("📊 RESUMEN DE PRUEBAS")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Pruebas pasadas: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 ¡TODAS LAS PRUEBAS PASARON!")
        print("\n✅ El bot está listo para usar.")
        print("\n📋 Próximos pasos:")
        print("   1. Habilita los intents privilegiados en Discord Developer Portal")
        print("   2. Ejecuta: python bot.py")
        print("   3. Invita el bot a tu servidor")
        print("   4. Usa: !setup #canal1 #canal2 #canal3")
    else:
        print(f"\n⚠️ {total - passed} prueba(s) fallaron.")
        print("\n🔧 Revisa los errores arriba y:")
        print("   1. Instala dependencias faltantes")
        print("   2. Configura el archivo .env")
        print("   3. Ejecuta este script nuevamente")
    
    print("\n" + "="*50)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️ Prueba cancelada por el usuario.")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        print("   Contacta al desarrollador para soporte.")