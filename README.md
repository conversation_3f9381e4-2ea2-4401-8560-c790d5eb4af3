# 🎉 Bot de Sorteos de Discord

Bot completo de Discord con sistema de sorteos avanzado, gestión de autorizaciones y funcionalidades adicionales.

## 🚀 Características Principales

### 🎊 Sistema de Sorteos
- **Sorteos por reacción**: Los usuarios participan reaccionando con 🎉
- **Condición de entrada obligatoria**: Requiere autorización para unir a otros servidores
- **Entradas adicionales**: Multiplicadores por roles específicos
- **Sorteos automáticos**: Por hitos de miembros y actividad

### 🔐 Gestión de Autorizaciones
- Control total de usuarios autorizados
- Estadísticas de autorizaciones
- Capacidad de invitar usuarios autorizados a otros servidores

### ✨ Funcionalidades Adicionales
- **Pings fantasma**: Mensajes de bienvenida que se eliminan automáticamente
- **Recompensas por actividad**: Sorteos rápidos basados en actividad del chat
- **Sorteos por hitos**: Automáticos al alcanzar cierto número de miembros

## 📋 Requisitos del Sistema

- **Python 3.8+**
- **RAM mínima**: 1GB (optimizado para servidores pequeños)
- **Almacenamiento**: 100MB
- **Conexión a internet estable**

## 🛠️ Instalación

### 1. Preparar el entorno
```bash
# Clonar o descargar los archivos
# Navegar al directorio del bot
cd ruta/al/bot

# Instalar dependencias
pip install -r requirements.txt
```

### 2. Configurar el bot en Discord

1. Ve a [Discord Developer Portal](https://discord.com/developers/applications)
2. Crea una nueva aplicación
3. Ve a la sección "Bot"
4. Crea un bot y copia el token
5. **Configurar Intents del Bot** (MUY IMPORTANTE):
   - En la sección "Bot", habilita TODOS los siguientes intents:
     - ✅ `Presence Intent`
     - ✅ `Server Members Intent` 
     - ✅ `Message Content Intent`
   - **NOTA**: Sin estos intents el bot NO funcionará correctamente

### 3. Configurar variables de entorno

```bash
# Copiar el archivo de ejemplo
cp .env.example .env

# Editar .env con tu token
DISCORD_TOKEN=tu_token_real_aqui
```

### 4. Invitar el bot al servidor

URL de invitación (reemplaza CLIENT_ID):
```
https://discord.com/api/oauth2/authorize?client_id=CLIENT_ID&permissions=8&scope=bot
```

**Permisos necesarios:**
- Administrar mensajes
- Enviar mensajes
- Leer historial de mensajes
- Agregar reacciones
- Usar emojis externos
- Gestionar roles (opcional)

## 🎮 Uso del Bot

### Comandos de Administración

#### Configuración inicial
```
!setup #canal-bienvenida #canal-actividad #canal-hitos
```
Configura los canales principales del servidor.

#### Crear sorteo
```
!giveaway 1h 1 Premio increíble
```
- `1h`: Duración (s=segundos, m=minutos, h=horas, d=días)
- `1`: Número de ganadores
- `Premio increíble`: Descripción del premio

#### Configurar multiplicadores de roles
```
!set_role_multiplier @RolVIP 3
```
Los usuarios con el rol tendrán 3x más probabilidades de ganar.

#### Estadísticas de autorización
```
!auth_stats
```
Muestra cuántos usuarios han autorizado el bot.

#### Invitar usuarios autorizados
```
!invite_users https://discord.gg/invite 25
```
Envía invitaciones a 25 usuarios autorizados.

### Comandos de Usuario

#### Autorizar el bot
```
!authorize
```
Proporciona el enlace para autorizar al bot.

#### Confirmar autorización
```
!confirm_auth
```
Confirma que has autorizado al bot.

## 🔧 Configuración Avanzada

### Base de Datos
El bot usa SQLite para almacenar:
- Usuarios autorizados
- Sorteos activos
- Configuración de servidores

### Archivos importantes
- `bot.py`: Código principal del bot
- `bot_data.db`: Base de datos SQLite (se crea automáticamente)
- `.env`: Variables de entorno (crear desde .env.example)
- `requirements.txt`: Dependencias de Python

### Personalización

Puedes modificar:
- Emojis de reacciones
- Mensajes del bot
- Tiempos de espera
- Límites de participantes

## 🚀 Ejecución

### Desarrollo
```bash
python bot.py
```

### Producción (recomendado)
```bash
# Con screen (Linux)
screen -S discord-bot python bot.py

# Con nohup (Linux)
nohup python bot.py &

# Con PM2 (Node.js ecosystem manager)
pm2 start bot.py --name discord-bot --interpreter python3
```

## 🌐 Hosting Recomendado

### Para 1GB RAM:
- **Vultr**: VPS desde $2.50/mes
- **DigitalOcean**: Droplet básico $4/mes
- **Linode**: Nanode 1GB $5/mes
- **Cybrance**: VPS económicos

### Configuración del servidor:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip git screen

# Clonar el bot
git clone <tu-repositorio>
cd discord-bot

# Instalar dependencias
pip3 install -r requirements.txt

# Configurar y ejecutar
cp .env.example .env
nano .env  # Editar con tu token
screen -S bot python3 bot.py
```

## 📊 Monitoreo y Logs

El bot incluye logging básico. Para monitoreo avanzado:

```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot.log'),
        logging.StreamHandler()
    ]
)
```

## 🔒 Seguridad

- **Nunca compartas tu token**
- Usa variables de entorno para datos sensibles
- Mantén el bot actualizado
- Revisa permisos regularmente

## 🐛 Solución de Problemas

### Error: Token inválido
- Verifica que el token en `.env` sea correcto
- Asegúrate de que el bot esté habilitado en Discord Developer Portal

### Error: Permisos insuficientes
- Verifica que el bot tenga los permisos necesarios
- Asegúrate de que el rol del bot esté por encima de los roles que gestiona

### Error: Base de datos
- Elimina `bot_data.db` para resetear la base de datos
- Verifica permisos de escritura en el directorio

### Problemas de memoria
- Monitorea el uso con `htop` o `ps aux`
- Considera aumentar la RAM si el servidor crece

## 📈 Escalabilidad

Para servidores grandes (>10,000 miembros):
- Considera PostgreSQL en lugar de SQLite
- Implementa sharding si es necesario
- Usa Redis para caché
- Monitorea rate limits de Discord

## 🤝 Soporte

Para soporte técnico:
1. Revisa los logs del bot
2. Verifica la configuración
3. Consulta la documentación de discord.py
4. Contacta al desarrollador: NightyWolf

## 📝 Licencia

Este bot fue desarrollado específicamente para el cliente y está sujeto a los términos acordados.

---

**Desarrollado por NightyWolf** 🐺

*Tiempo estimado de desarrollo: 9 horas para ejemplo funcional, 1-2 días para versión completa de producción.*