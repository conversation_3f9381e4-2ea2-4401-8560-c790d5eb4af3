import discord
from discord.ext import commands, tasks
import aiosqlite
import asyncio
import random
import os
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

load_dotenv()

# Configuración del bot
intents = discord.Intents.default()
intents.message_content = True
intents.members = True
intents.guilds = True
intents.reactions = True

bot = commands.Bot(command_prefix='!', intents=intents)

# Variables globales para configuración
config = {
    'welcome_channels': {},  # guild_id: channel_id
    'activity_channels': {},  # guild_id: {'channel_id': channel_id, 'message_count': count, 'current_messages': 0}
    'milestone_channels': {},  # guild_id: {'channel_id': channel_id, 'milestone': number, 'last_count': count}
    'role_multipliers': {}  # guild_id: {role_id: multiplier}
}

# Base de datos
async def init_db():
    async with aiosqlite.connect('bot_data.db') as db:
        # Tabla para usuarios autorizados
        await db.execute('''
            CREATE TABLE IF NOT EXISTS authorized_users (
                user_id INTEGER PRIMARY KEY,
                username TEXT,
                authorized_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Tabla para sorteos activos
        await db.execute('''
            CREATE TABLE IF NOT EXISTS active_giveaways (
                message_id INTEGER PRIMARY KEY,
                guild_id INTEGER,
                channel_id INTEGER,
                title TEXT,
                description TEXT,
                end_time TIMESTAMP,
                winner_count INTEGER,
                created_by INTEGER,
                participants TEXT DEFAULT '[]'
            )
        ''')
        
        # Tabla para configuración del servidor
        await db.execute('''
            CREATE TABLE IF NOT EXISTS guild_config (
                guild_id INTEGER PRIMARY KEY,
                welcome_channel INTEGER,
                activity_channel INTEGER,
                activity_message_count INTEGER DEFAULT 50,
                milestone_channel INTEGER,
                milestone_count INTEGER DEFAULT 500,
                last_member_count INTEGER DEFAULT 0
            )
        ''')
        
        await db.commit()

# Funciones de base de datos
async def add_authorized_user(user_id, username):
    async with aiosqlite.connect('bot_data.db') as db:
        await db.execute(
            'INSERT OR REPLACE INTO authorized_users (user_id, username) VALUES (?, ?)',
            (user_id, username)
        )
        await db.commit()

async def is_user_authorized(user_id):
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT user_id FROM authorized_users WHERE user_id = ?',
            (user_id,)
        )
        result = await cursor.fetchone()
        return result is not None

async def get_authorized_users_count():
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute('SELECT COUNT(*) FROM authorized_users')
        result = await cursor.fetchone()
        return result[0] if result else 0

async def get_authorized_users(limit=None):
    async with aiosqlite.connect('bot_data.db') as db:
        if limit:
            cursor = await db.execute(
                'SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC LIMIT ?',
                (limit,)
            )
        else:
            cursor = await db.execute('SELECT user_id, username FROM authorized_users ORDER BY authorized_at DESC')
        return await cursor.fetchall()

# Eventos del bot
@bot.event
async def on_ready():
    print(f'{bot.user} ha iniciado sesión!')
    await init_db()
    check_giveaways.start()
    print('Bot listo para usar!')

@bot.event
async def on_member_join(member):
    # Ping fantasma de bienvenida
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT welcome_channel FROM guild_config WHERE guild_id = ?',
            (member.guild.id,)
        )
        result = await cursor.fetchone()
        
        if result and result[0]:
            channel = bot.get_channel(result[0])
            if channel:
                welcome_msg = await channel.send(f'¡Bienvenido {member.mention}! 🎉')
                await asyncio.sleep(1)
                try:
                    await welcome_msg.delete()
                except:
                    pass
    
    # Verificar hitos de miembros
    await check_member_milestone(member.guild)

@bot.event
async def on_message(message):
    if message.author.bot:
        return
    
    # Verificar actividad para sorteos aleatorios
    await check_activity_reward(message)
    
    await bot.process_commands(message)

@bot.event
async def on_reaction_add(reaction, user):
    if user.bot:
        return
    
    # Verificar si es un sorteo
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT * FROM active_giveaways WHERE message_id = ?',
            (reaction.message.id,)
        )
        giveaway = await cursor.fetchone()
        
        if giveaway and str(reaction.emoji) == '🎉':
            # Verificar si el usuario está autorizado
            if not await is_user_authorized(user.id):
                try:
                    await user.send(
                        '❌ Para participar en sorteos, debes autorizar al bot para unirte a otros servidores.\n'
                        'Usa el comando `!authorize` en el servidor para obtener el enlace de autorización.'
                    )
                except:
                    pass
                return
            
            # Agregar participante
            participants = json.loads(giveaway[8]) if giveaway[8] else []
            if user.id not in participants:
                participants.append(user.id)
                await db.execute(
                    'UPDATE active_giveaways SET participants = ? WHERE message_id = ?',
                    (json.dumps(participants), reaction.message.id)
                )
                await db.commit()

# Comandos de administración
@bot.command(name='setup')
@commands.has_permissions(administrator=True)
async def setup_server(ctx, welcome_channel: discord.TextChannel = None, 
                      activity_channel: discord.TextChannel = None,
                      milestone_channel: discord.TextChannel = None):
    """Configura los canales del servidor"""
    async with aiosqlite.connect('bot_data.db') as db:
        await db.execute(
            'INSERT OR REPLACE INTO guild_config (guild_id, welcome_channel, activity_channel, milestone_channel) VALUES (?, ?, ?, ?)',
            (ctx.guild.id, 
             welcome_channel.id if welcome_channel else None,
             activity_channel.id if activity_channel else None,
             milestone_channel.id if milestone_channel else None)
        )
        await db.commit()
    
    embed = discord.Embed(title='✅ Configuración actualizada', color=0x00ff00)
    if welcome_channel:
        embed.add_field(name='Canal de bienvenida', value=welcome_channel.mention, inline=False)
    if activity_channel:
        embed.add_field(name='Canal de actividad', value=activity_channel.mention, inline=False)
    if milestone_channel:
        embed.add_field(name='Canal de hitos', value=milestone_channel.mention, inline=False)
    
    await ctx.send(embed=embed)

@bot.command(name='giveaway')
@commands.has_permissions(manage_messages=True)
async def create_giveaway(ctx, duration: str, winners: int, *, prize):
    """Crea un sorteo. Formato: !giveaway 1h 1 Premio increíble"""
    
    # Parsear duración
    time_units = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}
    duration_seconds = 0
    
    try:
        if duration[-1].lower() in time_units:
            duration_seconds = int(duration[:-1]) * time_units[duration[-1].lower()]
        else:
            duration_seconds = int(duration) * 60  # Default a minutos
    except:
        await ctx.send('❌ Formato de duración inválido. Usa: 30s, 5m, 2h, 1d')
        return
    
    if duration_seconds < 10:
        await ctx.send('❌ La duración mínima es 10 segundos.')
        return
    
    end_time = datetime.now() + timedelta(seconds=duration_seconds)
    
    embed = discord.Embed(
        title='🎉 ¡SORTEO! 🎉',
        description=f'**Premio:** {prize}\n'
                   f'**Ganadores:** {winners}\n'
                   f'**Termina:** <t:{int(end_time.timestamp())}:R>\n\n'
                   f'Reacciona con 🎉 para participar!\n'
                   f'⚠️ **Requisito:** Debes autorizar al bot para unirte a otros servidores.',
        color=0xff6b6b
    )
    embed.set_footer(text=f'Creado por {ctx.author.display_name}')
    
    message = await ctx.send(embed=embed)
    await message.add_reaction('🎉')
    
    # Guardar en base de datos
    async with aiosqlite.connect('bot_data.db') as db:
        await db.execute(
            'INSERT INTO active_giveaways (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            (message.id, ctx.guild.id, ctx.channel.id, prize, embed.description, end_time, winners, ctx.author.id)
        )
        await db.commit()

@bot.command(name='authorize')
async def authorize_user(ctx):
    """Proporciona el enlace de autorización para participar en sorteos"""
    auth_url = f'https://discord.com/api/oauth2/authorize?client_id={bot.user.id}&permissions=1&scope=bot%20guilds.join'
    
    embed = discord.Embed(
        title='🔐 Autorización requerida',
        description=f'Para participar en sorteos, debes autorizar al bot para unirte a otros servidores.\n\n'
                   f'[**Haz clic aquí para autorizar**]({auth_url})\n\n'
                   f'Una vez autorizado, usa `!confirm_auth` para confirmar.',
        color=0x3498db
    )
    
    try:
        await ctx.author.send(embed=embed)
        await ctx.send('📩 Te he enviado un mensaje privado con el enlace de autorización.')
    except:
        await ctx.send(embed=embed)

@bot.command(name='confirm_auth')
async def confirm_authorization(ctx):
    """Confirma la autorización del usuario"""
    await add_authorized_user(ctx.author.id, str(ctx.author))
    
    embed = discord.Embed(
        title='✅ Autorización confirmada',
        description='¡Ahora puedes participar en todos los sorteos!',
        color=0x00ff00
    )
    
    await ctx.send(embed=embed)

@bot.command(name='auth_stats')
@commands.has_permissions(administrator=True)
async def auth_stats(ctx):
    """Muestra estadísticas de usuarios autorizados"""
    count = await get_authorized_users_count()
    
    embed = discord.Embed(
        title='📊 Estadísticas de autorización',
        description=f'**Usuarios autorizados:** {count}',
        color=0x3498db
    )
    
    await ctx.send(embed=embed)

@bot.command(name='invite_users')
@commands.has_permissions(administrator=True)
async def invite_authorized_users(ctx, server_invite: str, amount: int = 10):
    """Invita usuarios autorizados a un servidor"""
    if amount > 50:
        await ctx.send('❌ Máximo 50 usuarios por comando.')
        return
    
    users = await get_authorized_users(amount)
    invited = 0
    
    for user_id, username in users:
        try:
            user = await bot.fetch_user(user_id)
            await user.send(
                f'¡Hola! Has sido invitado a un nuevo servidor: {server_invite}\n'
                f'Esta invitación es parte de los beneficios por estar autorizado en nuestro sistema de sorteos.'
            )
            invited += 1
            await asyncio.sleep(1)  # Evitar rate limits
        except:
            continue
    
    await ctx.send(f'✅ Invitaciones enviadas a {invited} usuarios autorizados.')

# Funciones auxiliares
async def check_activity_reward(message):
    """Verifica si se debe iniciar un sorteo por actividad"""
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT activity_channel, activity_message_count FROM guild_config WHERE guild_id = ?',
            (message.guild.id,)
        )
        result = await cursor.fetchone()
        
        if result and result[0] == message.channel.id:
            # Incrementar contador de mensajes
            # Aquí implementarías la lógica de conteo
            # Por simplicidad, usamos random para simular
            if random.randint(1, result[1] or 50) == 1:
                await start_quick_giveaway(message.channel)

async def start_quick_giveaway(channel):
    """Inicia un sorteo rápido por actividad"""
    prizes = ['💎 Gemas', '🎁 Premio sorpresa', '⭐ Rol especial', '🏆 Reconocimiento']
    prize = random.choice(prizes)
    
    embed = discord.Embed(
        title='⚡ ¡SORTEO RÁPIDO! ⚡',
        description=f'**Premio:** {prize}\n'
                   f'¡Primera persona en reaccionar gana!\n\n'
                   f'⚠️ **Requisito:** Debes estar autorizado.',
        color=0xffff00
    )
    
    message = await channel.send(embed=embed)
    await message.add_reaction('⚡')
    
    # Esperar primera reacción
    def check(reaction, user):
        return (reaction.message.id == message.id and 
                str(reaction.emoji) == '⚡' and 
                not user.bot)
    
    try:
        reaction, user = await bot.wait_for('reaction_add', timeout=30.0, check=check)
        
        if await is_user_authorized(user.id):
            winner_embed = discord.Embed(
                title='🎉 ¡Ganador del sorteo rápido!',
                description=f'¡Felicidades {user.mention}!\n'
                           f'Has ganado: **{prize}**',
                color=0x00ff00
            )
            await channel.send(embed=winner_embed)
        else:
            await channel.send(f'{user.mention}, necesitas estar autorizado para ganar. Usa `!authorize`.')
    except asyncio.TimeoutError:
        timeout_embed = discord.Embed(
            title='⏰ Sorteo rápido expirado',
            description='Nadie reaccionó a tiempo.',
            color=0xff0000
        )
        await channel.send(embed=timeout_embed)

async def check_member_milestone(guild):
    """Verifica si se alcanzó un hito de miembros"""
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT milestone_channel, milestone_count, last_member_count FROM guild_config WHERE guild_id = ?',
            (guild.id,)
        )
        result = await cursor.fetchone()
        
        if result and result[0]:
            current_count = guild.member_count
            milestone = result[1] or 500
            last_count = result[2] or 0
            
            if current_count >= last_count + milestone:
                channel = bot.get_channel(result[0])
                if channel:
                    await start_milestone_giveaway(channel, current_count)
                    
                    # Actualizar último conteo
                    await db.execute(
                        'UPDATE guild_config SET last_member_count = ? WHERE guild_id = ?',
                        (current_count, guild.id)
                    )
                    await db.commit()

async def start_milestone_giveaway(channel, member_count):
    """Inicia un sorteo por hito de miembros"""
    prizes = ['🎊 Premio especial por hito', '💰 Recompensa exclusiva', '🌟 Regalo de celebración']
    prize = random.choice(prizes)
    
    embed = discord.Embed(
        title='🎊 ¡SORTEO POR HITO! 🎊',
        description=f'¡Hemos alcanzado **{member_count}** miembros!\n\n'
                   f'**Premio:** {prize}\n'
                   f'**Ganadores:** 1\n'
                   f'**Duración:** 24 horas\n\n'
                   f'Reacciona con 🎊 para participar!\n'
                   f'⚠️ **Requisito:** Debes estar autorizado.',
        color=0xff1493
    )
    
    message = await channel.send(embed=embed)
    await message.add_reaction('🎊')
    
    # Guardar en base de datos
    end_time = datetime.now() + timedelta(hours=24)
    async with aiosqlite.connect('bot_data.db') as db:
        await db.execute(
            'INSERT INTO active_giveaways (message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            (message.id, channel.guild.id, channel.id, f'Hito {member_count} miembros', embed.description, end_time, 1, bot.user.id)
        )
        await db.commit()

@tasks.loop(minutes=1)
async def check_giveaways():
    """Verifica sorteos que han terminado"""
    async with aiosqlite.connect('bot_data.db') as db:
        cursor = await db.execute(
            'SELECT * FROM active_giveaways WHERE end_time <= ?',
            (datetime.now(),)
        )
        expired_giveaways = await cursor.fetchall()
        
        for giveaway in expired_giveaways:
            await end_giveaway(giveaway)
            
            # Eliminar de base de datos
            await db.execute(
                'DELETE FROM active_giveaways WHERE message_id = ?',
                (giveaway[0],)
            )
        
        await db.commit()

async def end_giveaway(giveaway_data):
    """Termina un sorteo y selecciona ganadores"""
    message_id, guild_id, channel_id, title, description, end_time, winner_count, created_by, participants = giveaway_data
    
    channel = bot.get_channel(channel_id)
    if not channel:
        return
    
    try:
        message = await channel.fetch_message(message_id)
    except:
        return
    
    participants_list = json.loads(participants) if participants else []
    
    if not participants_list:
        embed = discord.Embed(
            title='😢 Sorteo terminado',
            description=f'**Premio:** {title}\n\nNo hubo participantes válidos.',
            color=0xff0000
        )
        await channel.send(embed=embed)
        return
    
    # Seleccionar ganadores
    winners = random.sample(participants_list, min(winner_count, len(participants_list)))
    
    winner_mentions = []
    for winner_id in winners:
        try:
            user = await bot.fetch_user(winner_id)
            winner_mentions.append(user.mention)
        except:
            continue
    
    if winner_mentions:
        embed = discord.Embed(
            title='🎉 ¡Sorteo terminado!',
            description=f'**Premio:** {title}\n\n'
                       f'**Ganador(es):** {" ".join(winner_mentions)}\n\n'
                       f'¡Felicidades! 🎊',
            color=0x00ff00
        )
        await channel.send(embed=embed)
    else:
        embed = discord.Embed(
            title='😢 Sorteo terminado',
            description=f'**Premio:** {title}\n\nNo se pudieron contactar a los ganadores.',
            color=0xff0000
        )
        await channel.send(embed=embed)

# Comando para configurar multiplicadores de roles
@bot.command(name='set_role_multiplier')
@commands.has_permissions(administrator=True)
async def set_role_multiplier(ctx, role: discord.Role, multiplier: int):
    """Configura entradas extra para un rol específico"""
    if multiplier < 1 or multiplier > 10:
        await ctx.send('❌ El multiplicador debe estar entre 1 y 10.')
        return
    
    # Aquí guardarías en la base de datos
    embed = discord.Embed(
        title='✅ Multiplicador configurado',
        description=f'El rol {role.mention} ahora tiene **{multiplier}x** entradas en sorteos.',
        color=0x00ff00
    )
    
    await ctx.send(embed=embed)

if __name__ == '__main__':
    # El token debe estar en un archivo .env
    TOKEN = os.getenv('DISCORD_TOKEN')
    if not TOKEN:
        print('❌ Error: No se encontró el token de Discord en las variables de entorno.')
        print('Crea un archivo .env con: DISCORD_TOKEN=tu_token_aqui')
    else:
        bot.run(TOKEN)