"""
Giveaway commands with memory-optimized views and interactions.
Implements discord.py v2.0+ features with efficient resource management.
"""
import discord
from discord.ext import commands, tasks
import asyncio
import random
from typing import Optional, List
from datetime import datetime, timedelta

from core.logging import logger
from core.config import Colors, Emojis
from embeds.builders import GiveawayEmbed, EmbedTemplates, create_success_embed, create_error_embed
from interactions.views.base import BaseView
from utils.database import db_manager

class GiveawayView(BaseView):
    """Memory-efficient giveaway participation view"""
    
    def __init__(self, giveaway_data: dict):
        # Long timeout for giveaways
        super().__init__(timeout=None)  # No timeout for giveaways
        self.giveaway_data = giveaway_data
    
    @discord.ui.button(emoji=Emojis.GIVEAWAY, style=discord.ButtonStyle.primary, label="Enter Giveaway")
    async def enter_giveaway(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle giveaway entry"""
        try:
            # Check if user is authorized
            if not await db_manager.is_user_authorized(interaction.user.id):
                embed = EmbedTemplates.authorization_required()
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # Add participant to giveaway
            success = await db_manager.add_giveaway_participant(
                self.giveaway_data['message_id'],
                interaction.user.id
            )
            
            if success:
                embed = create_success_embed(
                    "✅ Entered Giveaway!",
                    f"You've successfully entered the giveaway for **{self.giveaway_data['title']}**!"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                logger.debug(f"User {interaction.user} entered giveaway {self.giveaway_data['message_id']}")
            else:
                embed = create_error_embed(
                    "Already Entered",
                    "You're already participating in this giveaway!"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in giveaway entry: {e}")
            embed = create_error_embed(
                "Error",
                "An error occurred while entering the giveaway. Please try again."
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)

class QuickGiveawayView(BaseView):
    """Memory-efficient quick giveaway view"""
    
    def __init__(self, prize: str, timeout: float = 30.0):
        super().__init__(timeout=timeout)
        self.prize = prize
        self.winner = None
    
    @discord.ui.button(emoji=Emojis.QUICK_GIVEAWAY, style=discord.ButtonStyle.success, label="Quick Enter!")
    async def quick_enter(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handle quick giveaway entry"""
        try:
            # Check if user is authorized
            if not await db_manager.is_user_authorized(interaction.user.id):
                embed = create_error_embed(
                    "Authorization Required",
                    f"{interaction.user.mention}, you need to be authorized to win. Use `!authorize`."
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            # First authorized user wins
            if not self.winner:
                self.winner = interaction.user
                
                # Disable button
                button.disabled = True
                
                embed = create_success_embed(
                    "🎉 Quick Giveaway Winner!",
                    f"Congratulations {interaction.user.mention}!\n"
                    f"You won: **{self.prize}**"
                )
                
                await interaction.response.edit_message(embed=embed, view=self)
                self.stop()
                
                logger.info(f"Quick giveaway won by {interaction.user}")
            else:
                embed = create_error_embed(
                    "Too Late!",
                    f"Someone else already won this quick giveaway!"
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in quick giveaway: {e}")

def setup_giveaway_commands(bot: commands.Bot):
    """Setup giveaway-related commands"""
    
    @bot.command(name='giveaway')
    @commands.has_permissions(manage_messages=True)
    async def create_giveaway(ctx, duration: str, winners: int, *, prize):
        """Create a giveaway. Format: !giveaway 1h 1 Amazing Prize"""
        
        # Parse duration
        time_units = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}
        duration_seconds = 0
        
        try:
            if duration[-1].lower() in time_units:
                duration_seconds = int(duration[:-1]) * time_units[duration[-1].lower()]
            else:
                duration_seconds = int(duration) * 60  # Default to minutes
        except (ValueError, IndexError):
            embed = create_error_embed(
                "Invalid Duration",
                "Use format: 30s, 5m, 2h, 1d"
            )
            await ctx.send(embed=embed)
            return
        
        if duration_seconds < 10:
            embed = create_error_embed(
                "Duration Too Short",
                "Minimum duration is 10 seconds."
            )
            await ctx.send(embed=embed)
            return
        
        if winners < 1 or winners > 20:
            embed = create_error_embed(
                "Invalid Winner Count",
                "Winner count must be between 1 and 20."
            )
            await ctx.send(embed=embed)
            return
        
        end_time = datetime.now() + timedelta(seconds=duration_seconds)
        
        # Create giveaway embed
        embed = GiveawayEmbed(
            "🎉 GIVEAWAY! 🎉",
            f"**Prize:** {prize}\n"
            f"**Winners:** {winners}\n"
            f"**Ends:** <t:{int(end_time.timestamp())}:R>\n\n"
            f"Click the button below to enter!\n"
            f"⚠️ **Requirement:** You must be authorized to participate."
        ).set_footer(f"Created by {ctx.author.display_name}").build()
        
        # Create giveaway data
        giveaway_data = {
            'message_id': 0,  # Will be set after sending
            'guild_id': ctx.guild.id,
            'channel_id': ctx.channel.id,
            'title': prize,
            'description': embed.description,
            'end_time': end_time,
            'winner_count': winners,
            'created_by': ctx.author.id
        }
        
        # Create view
        view = GiveawayView(giveaway_data)
        
        # Send message
        message = await ctx.send(embed=embed, view=view)
        
        # Update giveaway data with message ID
        giveaway_data['message_id'] = message.id
        view.giveaway_data['message_id'] = message.id
        
        # Save to database
        success = await db_manager.create_giveaway(
            message.id, ctx.guild.id, ctx.channel.id,
            prize, embed.description, end_time, winners, ctx.author.id
        )
        
        if success:
            # Register view for cleanup
            bot.register_view(f"giveaway_{message.id}", view)
            logger.info(f"Giveaway created: {prize} by {ctx.author}")
        else:
            embed = create_error_embed(
                "Database Error",
                "Failed to save giveaway to database."
            )
            await ctx.send(embed=embed)
    
    @bot.command(name='quick_giveaway', aliases=['qg'])
    @commands.has_permissions(manage_messages=True)
    async def quick_giveaway(ctx, *, prize: str = None):
        """Start a quick giveaway (first to react wins)"""
        
        if not prize:
            prizes = [
                '💎 Special Gems',
                '🎁 Mystery Prize',
                '⭐ Temporary Special Role',
                '🏆 Public Recognition',
                '🎨 Custom Avatar',
                '💰 Server Currency'
            ]
            prize = random.choice(prizes)
        
        embed = GiveawayEmbed(
            "⚡ QUICK GIVEAWAY! ⚡",
            f"**Prize:** {prize}\n"
            f"**Duration:** 30 seconds\n\n"
            f"First authorized person to click wins!\n"
            f"⚠️ **Requirement:** You must be authorized."
        ).build()
        
        view = QuickGiveawayView(prize, timeout=30.0)
        message = await ctx.send(embed=embed, view=view)
        
        # Register view for cleanup
        bot.register_view(f"quick_giveaway_{message.id}", view)
        
        logger.info(f"Quick giveaway started: {prize} by {ctx.author}")
    
    @tasks.loop(minutes=1)
    async def check_giveaways():
        """Check for expired giveaways"""
        try:
            expired_giveaways = await db_manager.get_expired_giveaways()
            
            for giveaway in expired_giveaways:
                await end_giveaway(giveaway)
                await db_manager.delete_giveaway(giveaway['message_id'])
                
        except Exception as e:
            logger.error(f"Error checking giveaways: {e}")
    
    async def end_giveaway(giveaway_data: dict):
        """End a giveaway and select winners"""
        try:
            channel = bot.get_channel(giveaway_data['channel_id'])
            if not channel:
                return
            
            try:
                message = await channel.fetch_message(giveaway_data['message_id'])
            except discord.NotFound:
                return
            
            participants = giveaway_data['participants']
            
            if not participants:
                embed = EmbedTemplates.giveaway_ended_no_participants()
                await channel.send(embed=embed)
                return
            
            # Select winners
            winner_count = min(giveaway_data['winner_count'], len(participants))
            winners = random.sample(participants, winner_count)
            
            # Get winner mentions
            winner_mentions = []
            for winner_id in winners:
                try:
                    user = await bot.fetch_user(winner_id)
                    winner_mentions.append(user.mention)
                except:
                    continue
            
            if winner_mentions:
                embed = create_success_embed(
                    "🎉 Giveaway Ended!",
                    f"**Prize:** {giveaway_data['title']}\n\n"
                    f"**Winner(s):** {' '.join(winner_mentions)}\n\n"
                    f"Congratulations! 🎊"
                )
                await channel.send(embed=embed)
                
                # Update original message
                try:
                    ended_embed = GiveawayEmbed(
                        "🎉 GIVEAWAY ENDED! 🎉",
                        f"**Prize:** {giveaway_data['title']}\n"
                        f"**Winners:** {' '.join(winner_mentions)}\n\n"
                        f"This giveaway has ended."
                    ).build()
                    
                    # Disable view
                    view = discord.ui.View()
                    button = discord.ui.Button(
                        emoji=Emojis.GIVEAWAY,
                        style=discord.ButtonStyle.secondary,
                        label="Giveaway Ended",
                        disabled=True
                    )
                    view.add_item(button)
                    
                    await message.edit(embed=ended_embed, view=view)
                except:
                    pass
                
                logger.info(f"Giveaway ended: {giveaway_data['title']} - {len(winner_mentions)} winners")
            else:
                embed = create_error_embed(
                    "Giveaway Ended",
                    f"**Prize:** {giveaway_data['title']}\n\n"
                    f"Could not contact the selected winners."
                )
                await channel.send(embed=embed)
                
        except Exception as e:
            logger.error(f"Error ending giveaway: {e}")
    
    # Start the giveaway checker
    check_giveaways.start()
    
    logger.info("Giveaway commands loaded")
