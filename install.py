#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de instalación automática para el Bot de Sorteos de Discord
Desarrollado por NightyWolf
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Muestra el banner de instalación"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🎉 BOT DE SORTEOS 🎉                     ║
    ║                                                              ║
    ║              Script de Instalación Automática               ║
    ║                   Desarrollado por NightyWolf               ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """Verifica la versión de Python"""
    print("🔍 Verificando versión de Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Error: Se requiere Python 3.8 o superior.")
        print(f"   Versión actual: {sys.version}")
        print("   Por favor, actualiza Python desde https://python.org")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detectado.")
    return True

def check_pip():
    """Verifica que pip esté disponible"""
    print("🔍 Verificando pip...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip está disponible.")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error: pip no está disponible.")
        print("   Instala pip desde https://pip.pypa.io/en/stable/installation/")
        return False

def install_requirements():
    """Instala las dependencias del bot"""
    print("📦 Instalando dependencias...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ Error: No se encontró requirements.txt")
        return False
    
    try:
        # Actualizar pip primero
        print("   Actualizando pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Instalar dependencias
        print("   Instalando paquetes...")
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            check=True,
            capture_output=True,
            text=True
        )
        
        print("✅ Dependencias instaladas correctamente.")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        print(f"   Salida del error: {e.stderr}")
        return False

def setup_env_file():
    """Configura el archivo .env"""
    print("⚙️ Configurando archivo de entorno...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("⚠️ El archivo .env ya existe.")
        response = input("   ¿Deseas sobrescribirlo? (s/N): ").lower().strip()
        if response != 's':
            print("   Manteniendo archivo .env existente.")
            return True
    
    if not env_example.exists():
        print("❌ Error: No se encontró .env.example")
        return False
    
    # Copiar archivo de ejemplo
    with open(env_example, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n📝 Configuración del bot:")
    print("   Para obtener el token del bot:")
    print("   1. Ve a https://discord.com/developers/applications")
    print("   2. Crea una nueva aplicación")
    print("   3. Ve a la sección 'Bot'")
    print("   4. Crea un bot y copia el token")
    print("   5. Habilita los intents necesarios")
    
    token = input("\n🔑 Ingresa el token del bot (o presiona Enter para configurar después): ").strip()
    
    if token:
        content = content.replace("tu_token_aqui", token)
        print("✅ Token configurado.")
    else:
        print("⚠️ Token no configurado. Recuerda editarlo en .env antes de ejecutar el bot.")
    
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Archivo .env creado.")
    return True

def create_startup_script():
    """Crea script de inicio"""
    print("🚀 Creando script de inicio...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # Script para Windows
        script_content = '''@echo off
echo Iniciando Bot de Sorteos...
python bot.py
pause
'''
        script_name = "start_bot.bat"
    else:
        # Script para Linux/Mac
        script_content = '''#!/bin/bash
echo "Iniciando Bot de Sorteos..."
python3 bot.py
'''
        script_name = "start_bot.sh"
    
    with open(script_name, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    if system != "windows":
        os.chmod(script_name, 0o755)
    
    print(f"✅ Script de inicio creado: {script_name}")
    return True

def verify_installation():
    """Verifica que la instalación sea correcta"""
    print("🔍 Verificando instalación...")
    
    # Verificar archivos principales
    required_files = ["bot.py", "requirements.txt", "config.py", ".env"]
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Archivos faltantes: {', '.join(missing_files)}")
        return False
    
    # Verificar que se puedan importar las dependencias básicas
    try:
        import aiosqlite
        import dotenv
        print("✅ Dependencias básicas verificadas.")
    except ImportError as e:
        print(f"⚠️ Advertencia importando dependencias: {e}")
        print("   El bot puede funcionar, pero verifica manualmente.")
    
    print("✅ Instalación verificada correctamente.")
    return True

def show_next_steps():
    """Muestra los siguientes pasos"""
    print("\n" + "="*60)
    print("🎉 ¡INSTALACIÓN COMPLETADA! 🎉")
    print("="*60)
    
    print("\n📋 Próximos pasos:")
    print("\n1. 🔑 Configurar el token del bot:")
    print("   - Edita el archivo .env")
    print("   - Reemplaza 'tu_token_aqui' con tu token real")
    
    print("\n2. 🤖 Invitar el bot a tu servidor:")
    print("   - Ve a Discord Developer Portal")
    print("   - Genera un enlace de invitación con permisos de administrador")
    print("   - Invita el bot a tu servidor")
    
    print("\n3. 🚀 Ejecutar el bot:")
    system = platform.system().lower()
    if system == "windows":
        print("   - Ejecuta: start_bot.bat")
        print("   - O ejecuta: python bot.py")
    else:
        print("   - Ejecuta: ./start_bot.sh")
        print("   - O ejecuta: python3 bot.py")
    
    print("\n4. ⚙️ Configurar el bot en Discord:")
    print("   - Usa !setup #canal-bienvenida #canal-actividad #canal-hitos")
    print("   - Configura roles con !set_role_multiplier @rol 2")
    
    print("\n📚 Comandos principales:")
    print("   !giveaway 1h 1 Premio - Crear sorteo")
    print("   !authorize - Autorizar usuario")
    print("   !auth_stats - Ver estadísticas")
    
    print("\n🆘 Soporte:")
    print("   - Lee el README.md para documentación completa")
    print("   - Contacta a NightyWolf para soporte técnico")
    
    print("\n" + "="*60)

def main():
    """Función principal de instalación"""
    print_banner()
    
    # Verificaciones previas
    if not check_python_version():
        sys.exit(1)
    
    if not check_pip():
        sys.exit(1)
    
    # Instalación
    steps = [
        ("Instalando dependencias", install_requirements),
        ("Configurando entorno", setup_env_file),
        ("Creando scripts", create_startup_script),
        ("Verificando instalación", verify_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        if not step_func():
            print(f"❌ Error en: {step_name}")
            sys.exit(1)
    
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Instalación cancelada por el usuario.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        print("   Contacta al desarrollador para soporte.")
        sys.exit(1)