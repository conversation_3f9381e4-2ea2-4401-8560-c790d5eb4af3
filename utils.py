# -*- coding: utf-8 -*-
"""
Utilidades adicionales para el Bot de Sorteos
Desarrollado por NightyWolf
"""

import asyncio
import aiosqlite
import json
import os
import shutil
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import discord
from discord.ext import commands

class DatabaseManager:
    """Gestor de base de datos con funciones de utilidad"""
    
    def __init__(self, db_path: str = "bot_data.db"):
        self.db_path = db_path
    
    async def backup_database(self, backup_dir: str = "backups") -> str:
        """Crea un backup de la base de datos"""
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(backup_dir, f"bot_data_backup_{timestamp}.db")
        
        shutil.copy2(self.db_path, backup_path)
        return backup_path
    
    async def get_database_stats(self) -> Dict[str, int]:
        """Obtiene estadísticas de la base de datos"""
        async with aiosqlite.connect(self.db_path) as db:
            stats = {}
            
            # Contar usuarios autorizados
            cursor = await db.execute("SELECT COUNT(*) FROM authorized_users")
            stats['authorized_users'] = (await cursor.fetchone())[0]
            
            # Contar sorteos activos
            cursor = await db.execute("SELECT COUNT(*) FROM active_giveaways")
            stats['active_giveaways'] = (await cursor.fetchone())[0]
            
            # Contar servidores configurados
            cursor = await db.execute("SELECT COUNT(*) FROM guild_config")
            stats['configured_guilds'] = (await cursor.fetchone())[0]
            
            return stats
    
    async def cleanup_expired_data(self) -> int:
        """Limpia datos expirados de la base de datos"""
        async with aiosqlite.connect(self.db_path) as db:
            # Eliminar sorteos muy antiguos (más de 30 días)
            cutoff_date = datetime.now() - timedelta(days=30)
            
            cursor = await db.execute(
                "DELETE FROM active_giveaways WHERE end_time < ?",
                (cutoff_date,)
            )
            
            deleted_count = cursor.rowcount
            await db.commit()
            
            return deleted_count

class GiveawayManager:
    """Gestor avanzado de sorteos"""
    
    @staticmethod
    def parse_duration(duration_str: str) -> int:
        """Convierte string de duración a segundos"""
        time_units = {
            's': 1, 'sec': 1, 'segundo': 1, 'segundos': 1,
            'm': 60, 'min': 60, 'minuto': 60, 'minutos': 60,
            'h': 3600, 'hour': 3600, 'hora': 3600, 'horas': 3600,
            'd': 86400, 'day': 86400, 'dia': 86400, 'dias': 86400
        }
        
        duration_str = duration_str.lower().strip()
        
        # Buscar unidad al final
        for unit, multiplier in time_units.items():
            if duration_str.endswith(unit):
                try:
                    number = int(duration_str[:-len(unit)])
                    return number * multiplier
                except ValueError:
                    continue
        
        # Si no hay unidad, asumir minutos
        try:
            return int(duration_str) * 60
        except ValueError:
            raise ValueError("Formato de duración inválido")
    
    @staticmethod
    def format_duration(seconds: int) -> str:
        """Convierte segundos a formato legible"""
        if seconds < 60:
            return f"{seconds}s"
        elif seconds < 3600:
            return f"{seconds // 60}m"
        elif seconds < 86400:
            return f"{seconds // 3600}h"
        else:
            return f"{seconds // 86400}d"
    
    @staticmethod
    async def get_user_entries(user: discord.Member, guild_config: Dict) -> int:
        """Calcula las entradas de un usuario basado en sus roles"""
        base_entries = 1
        role_multipliers = guild_config.get('role_multipliers', {})
        
        total_multiplier = 1
        for role in user.roles:
            if str(role.id) in role_multipliers:
                total_multiplier = max(total_multiplier, role_multipliers[str(role.id)])
        
        return base_entries * total_multiplier

class MessageFormatter:
    """Formateador de mensajes y embeds"""
    
    @staticmethod
    def create_giveaway_embed(title: str, description: str, end_time: datetime, 
                            winners: int, creator: discord.User, color: int = 0xff6b6b) -> discord.Embed:
        """Crea un embed para sorteos"""
        embed = discord.Embed(
            title='🎉 ¡SORTEO! 🎉',
            description=f'**Premio:** {title}\n'
                       f'**Ganadores:** {winners}\n'
                       f'**Termina:** <t:{int(end_time.timestamp())}:R>\n\n'
                       f'{description}\n\n'
                       f'Reacciona con 🎉 para participar!\n'
                       f'⚠️ **Requisito:** Debes autorizar al bot para unirte a otros servidores.',
            color=color
        )
        embed.set_footer(text=f'Creado por {creator.display_name}', icon_url=creator.avatar.url if creator.avatar else None)
        embed.timestamp = datetime.now()
        
        return embed
    
    @staticmethod
    def create_winner_embed(title: str, winners: List[discord.User], color: int = 0x00ff00) -> discord.Embed:
        """Crea un embed para anunciar ganadores"""
        winner_mentions = [winner.mention for winner in winners]
        
        embed = discord.Embed(
            title='🎉 ¡Sorteo terminado!',
            description=f'**Premio:** {title}\n\n'
                       f'**Ganador(es):** {" ".join(winner_mentions)}\n\n'
                       f'¡Felicidades! 🎊',
            color=color
        )
        embed.timestamp = datetime.now()
        
        return embed
    
    @staticmethod
    def create_stats_embed(stats: Dict, bot_user: discord.User) -> discord.Embed:
        """Crea un embed con estadísticas del bot"""
        embed = discord.Embed(
            title='📊 Estadísticas del Bot',
            color=0x3498db
        )
        
        embed.add_field(
            name='👥 Usuarios Autorizados',
            value=f"{stats.get('authorized_users', 0):,}",
            inline=True
        )
        
        embed.add_field(
            name='🎉 Sorteos Activos',
            value=f"{stats.get('active_giveaways', 0):,}",
            inline=True
        )
        
        embed.add_field(
            name='🏠 Servidores Configurados',
            value=f"{stats.get('configured_guilds', 0):,}",
            inline=True
        )
        
        embed.set_thumbnail(url=bot_user.avatar.url if bot_user.avatar else None)
        embed.timestamp = datetime.now()
        
        return embed

class SecurityManager:
    """Gestor de seguridad y validaciones"""
    
    @staticmethod
    def validate_giveaway_params(duration: int, winners: int, prize: str) -> Tuple[bool, str]:
        """Valida parámetros de sorteo"""
        if duration < 10:
            return False, "La duración mínima es 10 segundos"
        
        if duration > 604800:  # 1 semana
            return False, "La duración máxima es 1 semana"
        
        if winners < 1:
            return False, "Debe haber al menos 1 ganador"
        
        if winners > 20:
            return False, "Máximo 20 ganadores por sorteo"
        
        if len(prize) > 1000:
            return False, "La descripción del premio es muy larga (máximo 1000 caracteres)"
        
        if not prize.strip():
            return False, "Debes especificar un premio"
        
        return True, "Válido"
    
    @staticmethod
    def is_admin_or_owner(user: discord.Member) -> bool:
        """Verifica si el usuario es administrador o dueño"""
        return (user.guild_permissions.administrator or 
                user.guild_permissions.manage_guild or 
                user.id == user.guild.owner_id)
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Sanitiza entrada de usuario"""
        # Remover caracteres peligrosos
        dangerous_chars = ['@everyone', '@here', '<@&']
        
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        return text.strip()

class PerformanceMonitor:
    """Monitor de rendimiento del bot"""
    
    def __init__(self):
        self.command_stats = {}
        self.error_count = 0
        self.start_time = datetime.now()
    
    def log_command_usage(self, command_name: str):
        """Registra uso de comando"""
        if command_name not in self.command_stats:
            self.command_stats[command_name] = 0
        self.command_stats[command_name] += 1
    
    def log_error(self):
        """Registra un error"""
        self.error_count += 1
    
    def get_uptime(self) -> timedelta:
        """Obtiene tiempo de actividad"""
        return datetime.now() - self.start_time
    
    def get_stats(self) -> Dict:
        """Obtiene estadísticas de rendimiento"""
        return {
            'uptime': self.get_uptime(),
            'commands_used': sum(self.command_stats.values()),
            'most_used_command': max(self.command_stats.items(), key=lambda x: x[1]) if self.command_stats else None,
            'error_count': self.error_count,
            'command_breakdown': self.command_stats.copy()
        }

class ConfigManager:
    """Gestor de configuración"""
    
    def __init__(self, config_file: str = "bot_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Carga configuración desde archivo"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # Configuración por defecto
        return {
            'giveaway_settings': {
                'max_duration': 604800,
                'min_duration': 10,
                'max_winners': 20
            },
            'activity_settings': {
                'message_threshold': 50,
                'quick_giveaway_timeout': 30
            },
            'milestone_settings': {
                'default_milestone': 500,
                'milestone_duration': 86400
            }
        }
    
    def save_config(self):
        """Guarda configuración a archivo"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
    
    def get(self, key: str, default=None):
        """Obtiene valor de configuración"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value):
        """Establece valor de configuración"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()

class LogManager:
    """Gestor de logs personalizado"""
    
    def __init__(self, log_file: str = "bot.log"):
        self.log_file = log_file
    
    def log(self, level: str, message: str, user_id: Optional[int] = None, guild_id: Optional[int] = None):
        """Registra un mensaje en el log"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        if user_id:
            log_entry += f" | User: {user_id}"
        
        if guild_id:
            log_entry += f" | Guild: {guild_id}"
        
        # Escribir a archivo
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + "\n")
        
        # También imprimir en consola
        print(log_entry)
    
    def info(self, message: str, **kwargs):
        self.log("INFO", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self.log("WARNING", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self.log("ERROR", message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        self.log("DEBUG", message, **kwargs)

# Funciones de utilidad globales
def format_number(number: int) -> str:
    """Formatea números con separadores de miles"""
    return f"{number:,}"

def truncate_text(text: str, max_length: int = 100) -> str:
    """Trunca texto si es muy largo"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def get_random_color() -> int:
    """Obtiene un color aleatorio para embeds"""
    import random
    colors = [0x3498db, 0xe74c3c, 0x2ecc71, 0xf39c12, 0x9b59b6, 0x1abc9c, 0xe67e22]
    return random.choice(colors)

async def send_dm_safe(user: discord.User, content: str = None, embed: discord.Embed = None) -> bool:
    """Envía DM de forma segura, retorna True si fue exitoso"""
    try:
        if embed:
            await user.send(embed=embed)
        else:
            await user.send(content)
        return True
    except (discord.Forbidden, discord.HTTPException):
        return False

def create_progress_bar(current: int, total: int, length: int = 20) -> str:
    """Crea una barra de progreso ASCII"""
    if total == 0:
        return "[" + "─" * length + "]"
    
    filled = int(length * current / total)
    bar = "█" * filled + "─" * (length - filled)
    percentage = int(100 * current / total)
    
    return f"[{bar}] {percentage}%"

# Decoradores útiles
def require_authorization(func):
    """Decorador que requiere autorización del usuario"""
    async def wrapper(ctx, *args, **kwargs):
        # Aquí verificarías si el usuario está autorizado
        # Por simplicidad, asumimos que la verificación se hace en el comando
        return await func(ctx, *args, **kwargs)
    return wrapper

def admin_only(func):
    """Decorador que requiere permisos de administrador"""
    async def wrapper(ctx, *args, **kwargs):
        if not SecurityManager.is_admin_or_owner(ctx.author):
            await ctx.send("❌ No tienes permisos para usar este comando.")
            return
        return await func(ctx, *args, **kwargs)
    return wrapper