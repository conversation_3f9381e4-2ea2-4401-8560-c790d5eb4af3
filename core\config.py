"""
Memory-optimized configuration management for Discord bot.
Focuses on minimal memory footprint and efficient resource usage.
"""
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()

@dataclass
class MemoryConfig:
    """Memory optimization settings"""
    max_messages: Optional[int] = None  # Disable message cache
    max_cached_users: int = 100  # Limit user cache
    max_cached_guilds: int = 10  # Limit guild cache
    cleanup_interval: int = 300  # Cleanup every 5 minutes
    gc_threshold: int = 100  # Garbage collection threshold

@dataclass
class BotConfig:
    """Core bot configuration with memory optimization"""
    token: str
    command_prefix: str = "!"
    case_insensitive: bool = True
    strip_after_prefix: bool = True
    
    # Memory optimization
    memory: MemoryConfig = MemoryConfig()
    
    # Database settings
    database_url: str = "bot_data.db"
    database_pool_size: int = 5  # Small pool for memory efficiency
    
    # Logging
    log_level: str = "INFO"
    log_file: Optional[str] = None  # Memory: avoid file logging initially
    
    @classmethod
    def from_env(cls) -> 'BotConfig':
        """Create configuration from environment variables"""
        token = os.getenv('DISCORD_TOKEN')
        if not token:
            raise ValueError("DISCORD_TOKEN environment variable is required")
        
        return cls(
            token=token,
            command_prefix=os.getenv('COMMAND_PREFIX', '!'),
            database_url=os.getenv('DATABASE_URL', 'bot_data.db'),
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
        )

@dataclass
class IntentsConfig:
    """Minimal intents configuration for memory efficiency"""
    guilds: bool = True
    guild_messages: bool = True
    guild_reactions: bool = True
    message_content: bool = True
    # Disabled for memory optimization
    members: bool = False
    presences: bool = False
    typing: bool = False
    voice_states: bool = False
    guild_scheduled_events: bool = False

# Color constants for embeds
class Colors:
    SUCCESS = 0x00ff00
    ERROR = 0xff0000
    WARNING = 0xffff00
    INFO = 0x3498db
    GIVEAWAY = 0xff6b6b
    QUICK_GIVEAWAY = 0xffff00
    MILESTONE = 0xff1493
    DARK_EMBED = 0x2B2D31
    LIGHT_EMBED = 0x24272A

# Emoji constants
class Emojis:
    GIVEAWAY = "🎉"
    QUICK_GIVEAWAY = "⚡"
    MILESTONE = "🎊"
    SUCCESS = "✅"
    ERROR = "❌"
    WARNING = "⚠️"
    INFO = "ℹ️"

# Global configuration instance
config = BotConfig.from_env()
intents_config = IntentsConfig()
