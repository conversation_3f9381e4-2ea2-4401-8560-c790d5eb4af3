"""
Custom exceptions for the Discord bot.
Memory-efficient exception hierarchy.
"""

class BotException(Exception):
    """Base exception for bot-related errors"""
    pass

class ConfigurationError(BotException):
    """Raised when there's a configuration issue"""
    pass

class DatabaseError(BotException):
    """Raised when there's a database-related error"""
    pass

class AuthorizationError(BotException):
    """Raised when user authorization fails"""
    pass

class GiveawayError(BotException):
    """Raised when there's a giveaway-related error"""
    pass

class ViewError(BotException):
    """Raised when there's a view-related error"""
    pass

class MemoryLimitError(BotException):
    """Raised when memory limits are exceeded"""
    pass
